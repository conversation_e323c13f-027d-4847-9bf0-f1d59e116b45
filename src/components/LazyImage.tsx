import { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Skeleton } from '@/components/ui/skeleton';

interface LazyImageProps {
  src: string;
  alt: string;
  className?: string;
  containerClassName?: string;
  size?: 'thumbnail' | 'card' | 'full';
  showSkeleton?: boolean;
}

const sizeClasses = {
  thumbnail: 'w-16 h-16',
  card: 'w-full h-full',
  full: 'w-full h-full'
};

export function LazyImage({ 
  src, 
  alt, 
  className = '', 
  containerClassName = '',
  size = 'card',
  showSkeleton = true 
}: LazyImageProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const [hasError, setHasError] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px'
      }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const handleLoad = () => {
    setIsLoaded(true);
  };

  const handleError = () => {
    setHasError(true);
    setIsLoaded(true);
  };

  return (
    <div 
      ref={containerRef}
      className={cn(
        'relative overflow-hidden',
        sizeClasses[size],
        containerClassName
      )}
    >
      {showSkeleton && !isLoaded && (
        <Skeleton className="absolute inset-0 z-10" />
      )}
      
      {isInView && (
        <img
          ref={imgRef}
          src={src}
          alt={alt}
          loading="lazy"
          onLoad={handleLoad}
          onError={handleError}
          className={cn(
            'object-cover transition-opacity duration-300',
            isLoaded ? 'opacity-100' : 'opacity-0',
            hasError && 'bg-muted',
            className
          )}
          style={{
            filter: isLoaded ? 'none' : 'blur(8px)',
          }}
        />
      )}
      
      {hasError && isLoaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-muted text-muted-foreground text-sm">
          Failed to load
        </div>
      )}
    </div>
  );
}