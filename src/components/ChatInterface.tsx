
import { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent } from "@/components/ui/card";
import { Send, User, Sparkles, Heart, Star } from "lucide-react";
import { VoiceInput } from "./VoiceInput";
import { SimpleLoadingScreen } from "./SimpleLoadingScreen";

interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
}

interface ChatInterfaceProps {
  onEditRequest: (editInstructions: string, baseImageUrl: string, originalPrompt: string) => void;
  initialMessage?: string;
  isProcessing?: boolean;
  currentImageUrl?: string;
  originalPrompt?: string;
  editHistory?: Array<{
    imageUrl: string;
    instruction: string;
    timestamp: Date;
  }>;
}

export function ChatInterface({ 
  onEditRequest, 
  initialMessage, 
  isProcessing = false, 
  currentImageUrl,
  originalPrompt = "",
  editHistory = []
}: ChatInterfaceProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const scrollRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Always reset messages when component mounts or initialMessage changes
    if (initialMessage) {
      setMessages([{
        id: `initial-${Date.now()}`, // Make ID unique to force refresh
        type: 'ai',
        content: "Hi there! I'm Pixi! 🧚‍♀️✨ I can help you make edits to your current image or create a brand new character! Just tell me what you'd like - like 'make it more magical', 'add fire blasters', or 'change the colors'! 🔥",
        timestamp: new Date()
      }]);
    }
  }, [initialMessage, currentImageUrl]); // Add currentImageUrl to force refresh when image changes

  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [messages]);

  const handleSend = async () => {
    if (!input.trim() || isLoading || isProcessing) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: input,
      timestamp: new Date()
    };

    // Add user message
    setMessages(prev => [...prev, userMessage]);
    
    // Create unique AI response based on user input  
    const responses = [
      "Great edit idea! 🎨 I'll apply those changes to your character. Here we go! ✨",
      "Perfect! 🌟 Let me edit your image with that request. Working my magic! ✨", 
      "Awesome! 🎭 Applying those changes to your character. This will look great! ✨",
      "Love it! 🦄 I'll modify your image with that. Edit in progress! ✨"
    ];
    
    const randomResponse = responses[Math.floor(Math.random() * responses.length)];
    
    const aiResponse: Message = {
      id: `ai-${Date.now()}`,
      type: 'ai',
      content: randomResponse,
      timestamp: new Date()
    };

    // Add AI response
    setMessages(prev => [...prev, aiResponse]);
    setInput("");
    setIsLoading(true);

    // Trigger the edit request with current image and original context
    if (currentImageUrl) {
      onEditRequest(input, currentImageUrl, originalPrompt);
    }
    
    setIsLoading(false);
  };

  const handleVoiceTranscript = (text: string) => {
    setInput(prev => prev + " " + text);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <>
      <SimpleLoadingScreen isVisible={isProcessing} />
      <Card className="h-full flex flex-col bg-gradient-to-br from-purple-50/50 to-pink-50/50 dark:from-purple-950/20 dark:to-pink-950/20 border-2 border-gradient-to-r from-purple-300 to-pink-300 dark:from-purple-700 dark:to-pink-700 rounded-2xl shadow-lg">
        <CardContent className="p-6 flex-1 flex flex-col">
          <div className="mb-6 text-center">
            <div className="flex items-center justify-center gap-2 mb-2">
              <Star className="w-5 h-5 text-yellow-500 animate-pulse" />
            <h3 className="text-lg font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              Edit Your Character
            </h3>
              <Heart className="w-5 h-5 text-pink-500 animate-pulse" />
            </div>
            <p className="text-sm text-muted-foreground font-medium mb-4">Ask Pixi what you'd like to change! 🧚‍♀️✨</p>
            
            {/* Voice Input buttons moved here, below the instruction text */}
            <div className="flex justify-center">
              <VoiceInput 
                onTranscript={handleVoiceTranscript}
                onAIGenerate={() => {}}
                disabled={isLoading || isProcessing}
                showAIWand={false}
              />
            </div>
          </div>
        
        <ScrollArea ref={scrollRef} className="flex-1 mb-4 pr-4">
          <div className="space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex items-start gap-3 ${
                  message.type === 'user' ? 'justify-end' : 'justify-start'
                }`}
              >
                {message.type === 'ai' && (
                  <div className="w-10 h-10 rounded-full bg-gradient-to-br from-purple-400 to-pink-400 flex items-center justify-center shadow-lg">
                    <Sparkles className="w-5 h-5 text-white animate-pulse" />
                  </div>
                )}
                <div
                  className={`max-w-[80%] rounded-2xl p-4 shadow-md ${
                    message.type === 'user'
                      ? 'bg-gradient-to-br from-blue-500 to-purple-600 text-white'
                      : 'bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900/30 dark:to-pink-900/30 text-foreground border border-purple-200 dark:border-purple-700'
                  }`}
                >
                  <p className="text-sm">{message.content}</p>
                  <p className="text-xs opacity-70 mt-1">
                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </p>
                </div>
                {message.type === 'user' && (
                  <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-400 to-cyan-400 flex items-center justify-center shadow-lg">
                    <User className="w-5 h-5 text-white" />
                  </div>
                )}
              </div>
            ))}
{/* Loading handled by SimpleLoadingScreen */}
          </div>
        </ScrollArea>

        <div className="flex gap-2">
          <div className="relative flex-1">
            <Textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask Pixi what you'd like to change! 🧚‍♀️✨"
              disabled={isLoading || isProcessing}
              className="rounded-xl border-2 border-purple-200 dark:border-purple-700 focus:border-purple-400 dark:focus:border-purple-500 bg-white/50 dark:bg-gray-900/50 min-h-[40px] max-h-[120px] resize-none"
              rows={1}
            />
          </div>
          <Button
            onClick={handleSend}
            disabled={!input.trim() || isLoading || isProcessing}
            size="icon"
            className="rounded-xl bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 shadow-lg"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
        </CardContent>
      </Card>
    </>
  );
}
