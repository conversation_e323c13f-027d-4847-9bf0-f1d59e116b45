import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { ArrowLeft, Download, CheckSquare, Search, Database, ShoppingCart, Eye, X, ExternalLink, Plus, Edit2, Trash2, Save, AlertCircle } from 'lucide-react';
import html2canvas from 'html2canvas';
import { createPrintCardHTML } from '@/components/PrintCharacterCard';
import UpdateStories from '@/components/UpdateStories';
import UpdateDescriptors from '@/components/UpdateDescriptors';
import GenerateStats from '@/components/GenerateStats';



interface AdminPrompt {
  id: string;
  name: string;
  prompt_text: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface PromptHistoryData {
  id: string;
  user_prompt: string;
  processed_prompt: string;
  dalle_revised_prompt?: string | null;
  image_url?: string | null;
  card_id?: string | null;
  user_id?: string | null;
  session_id?: string | null;
  generation_status: string;
  error_message?: string | null;
  metadata: any;
  created_at: string;
  updated_at: string;
}

interface CardData {
  id: string;
  name: string;
  backstory?: string | null;
  signature?: string | null;
  image_url: string;
  image_description?: string | null;
  level: number;
  likes?: number;
  power_stat?: number | null;
  magic_stat?: number | null;
  speed_stat?: number | null;
  created_at: string;
  user_id?: string | null;
  source?: 'leaderboard' | 'user';
}

interface OrderData {
  id: string;
  customer_name: string;
  customer_email: string;
  customer_address: string;
  customer_city: string;
  customer_state: string;
  customer_zip: string;
  customer_country: string;
  customer_phone?: string | null;
  order_items: any;
  total_quantity: number;
  total_amount: number;
  status: string;
  special_instructions?: string | null;
  created_at: string;
  updated_at: string;
}

const Admin = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [cards, setCards] = useState<CardData[]>([]);
  const [allCards, setAllCards] = useState<CardData[]>([]);
  const [orders, setOrders] = useState<OrderData[]>([]);
  const [selectedCards, setSelectedCards] = useState<Set<string>>(new Set());
  const [searchTerm, setSearchTerm] = useState('');
  const [cardSearchTerm, setCardSearchTerm] = useState('');
  const [orderSearchTerm, setOrderSearchTerm] = useState('');
  const [downloadProgress, setDownloadProgress] = useState<{current: number, total: number} | null>(null);
  const [previewCard, setPreviewCard] = useState<CardData | null>(null);
  const [imageSettings, setImageSettings] = useState<any>(null);
  const [activeTab, setActiveTab] = useState('overview');
  
  // Prompt management state
  const [prompts, setPrompts] = useState<AdminPrompt[]>([]);
  const [promptSearchTerm, setPromptSearchTerm] = useState('');
  const [editingPrompt, setEditingPrompt] = useState<AdminPrompt | null>(null);
  const [isAddingPrompt, setIsAddingPrompt] = useState(false);
  const [newPrompt, setNewPrompt] = useState({ name: '', prompt_text: '', is_active: true });
  
  // Prompt history state
  const [promptHistory, setPromptHistory] = useState<PromptHistoryData[]>([]);
  const [historySearchTerm, setHistorySearchTerm] = useState('');
  const [selectedHistoryItem, setSelectedHistoryItem] = useState<PromptHistoryData | null>(null);
  
  // Descriptor prompt template - this is what's used to generate card superpowers
  const descriptorPromptTemplate = {
    systemPrompt: "You are a creative writer for a kid-friendly trading card game. Generate single-line superpower descriptions that include the character's name and describe what they can do.",
    userPrompt: `Generate a superpower description for {characterName}, a kid-friendly character for ages 6-12.

Character: {characterName}
Character appearance/theme: {imageDescription}

Requirements:
- Must be 60 characters or fewer
- Include the character's name in the description
- Format: [Character name] [action/ability] - no quotes or punctuation at the end
- Kid-friendly, positive, and exciting
- Focus on what they can DO, not accomplishments

Examples:
- Firey launches blazing fire rockets from her hands
- Speedy zooms faster than lightning bolts
- Magicy creates rainbow bridges in the sky
- Luna controls the power of moonbeams

Generate ONLY the superpower text for {characterName}, no quotes, no extra text`
  };

  // Stats generation prompt template
  const statsPromptTemplate = `You are generating balanced character stats for a trading card game.

STEP 1: Analyze the superpower to identify which stat should be STRONG:
- If superpower mentions physical strength, fighting, muscles, armor, toughness → Power is STRONG
- If superpower mentions magic, spells, supernatural abilities, special powers, creativity → Magic is STRONG  
- If superpower mentions speed, flying, quick movement, agility, reactions → Speed is STRONG

STEP 2: Generate stats with this EXACT distribution:
- STRONG stat (matches superpower): 75-90
- MODERATE stat (somewhat relevant): 45-65
- WEAK stat (least relevant): 25-45

STEP 3: Ensure good spread - stats should be at least 20 points apart from each other.

Return ONLY a JSON object: {"power": [number], "magic": [number], "speed": [number]}`;
  
  const navigate = useNavigate();
  const { toast } = useToast();

  // Helper function to calculate generation time
  const calculateGenerationTime = (history: PromptHistoryData): string => {
    if (!history.metadata || typeof history.metadata !== 'object') return 'N/A';
    
    const metadata = history.metadata as any;
    if (metadata.generation_start_time && metadata.generation_end_time) {
      const startTime = new Date(metadata.generation_start_time).getTime();
      const endTime = new Date(metadata.generation_end_time).getTime();
      const durationMs = endTime - startTime;
      
      if (durationMs < 1000) {
        return `${durationMs}ms`;
      } else {
        return `${(durationMs / 1000).toFixed(1)}s`;
      }
    }
    
    return 'N/A';
  };

  // Helper function to format creator information
  const formatCreator = (signature: string | null): string => {
    if (!signature) return 'Anonymous';
    
    // If it already starts with "Created by", return as is
    if (signature.toLowerCase().startsWith('created by')) {
      return signature;
    }
    
    // Otherwise, add "created by" prefix
    return `created by ${signature}`;
  };

  // Simple password protection
  const ADMIN_PASSWORD = 'pixiadmin2024'; // In production, this should be in environment variables

  const handlePasswordSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (password === ADMIN_PASSWORD) {
      setIsAuthenticated(true);
      loadCards();
      loadAllCards();
      loadOrders();
      loadPrompts();
      loadPromptHistory();
      loadImageSettings();
    } else {
      toast({
        title: "Access Denied",
        description: "Incorrect password",
        variant: "destructive",
      });
    }
  };

  const loadCards = async () => {
    try {
      const { data, error } = await supabase
        .from('leaderboard_cards')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setCards(data || []);
    } catch (error) {
      console.error('Error loading cards:', error);
      toast({
        title: "Error",
        description: "Failed to load cards",
        variant: "destructive",
      });
    }
  };

  const loadAllCards = async () => {
    try {
      console.log('Loading all cards...');
      
      // Load leaderboard cards
      const { data: leaderboardCards, error: leaderboardError } = await supabase
        .from('leaderboard_cards')
        .select('*')
        .order('created_at', { ascending: false });

      if (leaderboardError) throw leaderboardError;
      console.log('Leaderboard cards loaded:', leaderboardCards?.length || 0);

      // Load user cards
      const { data: userCards, error: userError } = await supabase
        .from('user_cards')
        .select('*')
        .order('created_at', { ascending: false });

      if (userError) throw userError;
      console.log('User cards loaded:', userCards?.length || 0);

      // Combine both datasets with source indicator
      const combined = [
        ...(leaderboardCards || []).map(card => ({ ...card, source: 'leaderboard' as const })),
        ...(userCards || []).map(card => ({ ...card, likes: 0, source: 'user' as const }))
      ];

      console.log('Total combined cards:', combined.length);
      console.log('Sample cards:', combined.slice(0, 3));
      
      setAllCards(combined);
    } catch (error) {
      console.error('Error loading all cards:', error);
      toast({
        title: "Error",
        description: "Failed to load card data",
        variant: "destructive",
      });
    }
  };

  const loadImageSettings = async () => {
    try {
      const { data, error } = await supabase
        .from('image_generation_settings')
        .select('*')
        .maybeSingle();

      if (error) throw error;
      setImageSettings(data);
    } catch (error) {
      console.error('Error loading image settings:', error);
    }
  };

  const updateImageQuality = async (quality: 'standard' | 'hd') => {
    try {
      if (!imageSettings) return;

      const updatedSettings = {
        ...imageSettings,
        dalle_settings: {
          ...imageSettings.dalle_settings,
          quality: quality
        }
      };

      const { error } = await supabase
        .from('image_generation_settings')
        .update({ dalle_settings: updatedSettings.dalle_settings })
        .eq('id', imageSettings.id);

      if (error) throw error;

      setImageSettings(updatedSettings);
      toast({
        title: "Settings Updated",
        description: `Image quality set to ${quality}`,
      });
    } catch (error) {
      console.error('Error updating image settings:', error);
      toast({
        title: "Error",
        description: "Failed to update settings",
        variant: "destructive"
      });
    }
  };

  const loadOrders = async () => {
    try {
      const { data, error } = await supabase
        .from('bulk_orders')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setOrders(data || []);
    } catch (error) {
      console.error('Error loading orders:', error);
      toast({
        title: "Error",
        description: "Failed to load orders",
        variant: "destructive",
      });
    }
  };

  const handleCardSelection = (cardId: string, selected: boolean) => {
    const newSelection = new Set(selectedCards);
    if (selected) {
      newSelection.add(cardId);
    } else {
      newSelection.delete(cardId);
    }
    setSelectedCards(newSelection);
  };

  // Filter cards based on search term
  const filteredCards = cards.filter(card => 
    card.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (card.backstory || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (card.signature || '').toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Filter all cards for the table view
  const filteredAllCards = allCards.filter(card => 
    card.name.toLowerCase().includes(cardSearchTerm.toLowerCase()) ||
    (card.backstory || '').toLowerCase().includes(cardSearchTerm.toLowerCase()) ||
    (card.signature || '').toLowerCase().includes(cardSearchTerm.toLowerCase())
  );

  console.log('All cards count:', allCards.length);
  console.log('Card search term:', cardSearchTerm);
  console.log('Filtered all cards count:', filteredAllCards.length);

  // Filter orders based on search term
  const filteredOrders = orders.filter(order => 
    order.customer_name.toLowerCase().includes(orderSearchTerm.toLowerCase()) ||
    order.customer_email.toLowerCase().includes(orderSearchTerm.toLowerCase()) ||
    order.status.toLowerCase().includes(orderSearchTerm.toLowerCase())
  );

  const selectAllCards = () => {
    if (selectedCards.size === filteredCards.length) {
      setSelectedCards(new Set());
    } else {
      setSelectedCards(new Set(filteredCards.map(card => card.id)));
    }
  };

  // Helper function to detect if a description is a technical prompt
  const isTechnicalPrompt = (description: string): boolean => {
    if (!description) return false;
    const technicalKeywords = [
      'generate an image',
      'generate a picture',
      'illustrate a',
      'illustrate an',
      'cartoonish style',
      'cartoon-style',
      'child-friendly',
      'kid-friendly',
      'void of any text',
      'imagine',
      'create an image',
      'digital art style',
      'illustration style',
      'artwork style',
      'defined with',
      'aesthetics',
      'characterized by',
      'endearing image',
      'appealing'
    ];
    const lowercaseDesc = description.toLowerCase();
    return technicalKeywords.some(keyword => lowercaseDesc.includes(keyword));
  };


  // Helper to normalize to a single line and cap length for print
  const toOneLineDescription = (text: string, max = 60): string => {
    if (!text) return '';
    const single = text.replace(/\s+/g, ' ').trim();
    if (single.length <= max) return single;
    const cut = single.slice(0, max);
    const lastSpace = cut.lastIndexOf(' ');
    const base = (lastSpace > 0 ? cut.slice(0, lastSpace) : cut);
    return base.replace(/[.!?…-]+$/, '');
  };
  
  const downloadSingleCard = async (card: CardData, index: number, total: number): Promise<boolean> => {
    console.log(`Starting download for card: ${card.name} (${index + 1}/${total})`);
    
    try {
      // Validate card data first
      if (!card.name || !card.image_url) {
        console.error(`Card ${card.name || 'Unknown'} missing required data`);
        toast({
          title: "Download Error",
          description: `Card "${card.name || 'Unknown'}" is missing required data`,
          variant: "destructive",
        });
        return false;
      }
      
      // Update progress
      setDownloadProgress({ current: index + 1, total });
      
      // Always prefer backstory; ignore legacy image_description, then normalize to one line
      const rawDescription = (card.backstory && card.backstory.trim().length > 0) ? card.backstory : '';
      const oneLineDescription = toOneLineDescription(rawDescription, 60);
      
      console.log(`Converting image to data URL for: ${card.name}`);
      
      // Convert image to data URL to avoid CORS issues completely
      const imageDataUrl = await new Promise<string>((resolve, reject) => {
        const img = new Image();
        img.crossOrigin = 'anonymous';
        
        img.onload = () => {
          try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            if (!ctx) {
              reject(new Error('Could not get canvas context'));
              return;
            }
            
            canvas.width = img.naturalWidth;
            canvas.height = img.naturalHeight;
            ctx.drawImage(img, 0, 0);
            
            const dataUrl = canvas.toDataURL('image/png');
            console.log(`Successfully converted image to data URL for ${card.name}`);
            resolve(dataUrl);
          } catch (error) {
            console.warn(`Failed to convert image to data URL for ${card.name}:`, error);
            // Fallback to original URL
            resolve(card.image_url);
          }
        };
        
        img.onerror = (error) => {
          console.warn(`Failed to load image for conversion for ${card.name}:`, error);
          // Fallback to original URL
          resolve(card.image_url);
        };
        
        img.src = card.image_url;
        
        // Timeout after 10 seconds
        setTimeout(() => {
          console.warn(`Image conversion timeout for ${card.name}, using original URL`);
          resolve(card.image_url);
        }, 10000);
      });
      
      // Create a hidden container for rendering - off-screen
      const tempContainer = document.createElement('div');
      tempContainer.style.cssText = `
        position: fixed;
        top: -10000px;
        left: -10000px;
        width: 1200px;
        height: 1500px;
        margin: 0;
        padding: 0;
        font-family: ui-sans-serif, system-ui, sans-serif;
        background: #f3f4f6;
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        z-index: -9999;
        visibility: hidden;
      `;
      
      // Use the print card HTML generation with data URL
      const { createPrintCardHTML } = await import('../components/PrintCharacterCard');
      const cardHTML = createPrintCardHTML({
        imageUrl: imageDataUrl, // Use data URL instead of original URL
        name: card.name,
        description: oneLineDescription,
        creator: card.signature || '',
        likes: card.likes,
        powerStat: card.power_stat,
        magicStat: card.magic_stat,
        speedStat: card.speed_stat
      });
      
      tempContainer.innerHTML = cardHTML;
      document.body.appendChild(tempContainer);
      
      // Wait for the HTML to render and img tag to load the data URL
      await new Promise<void>((resolve) => {
        const img = tempContainer.querySelector('img');
        if (img && img.complete) {
          console.log(`Data URL image already loaded for ${card.name}`);
          resolve();
        } else if (img) {
          img.onload = () => {
            console.log(`Data URL image loaded for ${card.name}`);
            resolve();
          };
          img.onerror = () => {
            console.warn(`Data URL image failed to load for ${card.name}`);
            resolve(); // Continue even if error
          };
        } else {
          resolve();
        }
        // Timeout fallback
        setTimeout(() => {
          console.log(`Timeout waiting for image load for ${card.name}`);
          resolve();
        }, 2000);
      });
      
      // Additional delay to ensure DOM is fully ready
      await new Promise(resolve => setTimeout(resolve, 500));
      
      console.log(`Capturing canvas for ${card.name}...`);
      
      // Position off-screen but keep visible so html2canvas can render it
      tempContainer.style.position = 'fixed';
      tempContainer.style.top = '-9999px';
      tempContainer.style.left = '-9999px';
      tempContainer.style.visibility = 'visible';
      tempContainer.style.pointerEvents = 'none';
      tempContainer.style.zIndex = '-1';
      
      // Capture the entire container with improved settings
      const canvas = await html2canvas(tempContainer, {
        useCORS: false, // Changed from true to false to avoid CORS issues
        allowTaint: true, // Changed from false to true to allow cross-origin images
        backgroundColor: null,
        scale: 3,
        scrollX: 0,
        scrollY: 0,
        width: 1200,
        height: 1500,
        foreignObjectRendering: false,
        imageTimeout: 15000,
        logging: true // Enable logging for debugging
      });
      
      console.log(`Canvas created: ${canvas.width}x${canvas.height} for ${card.name}`);
      console.log(`Aspect ratio: ${(canvas.width / canvas.height).toFixed(3)} (target: 0.735)`);
      
      // Validate canvas
      if (!canvas || canvas.width === 0 || canvas.height === 0) {
        throw new Error(`Invalid canvas for card: ${card.name}`);
      }
      
      // Canvas is already the correct print size (815×1110) with bleed
      const printCanvas = canvas;
      
      // Create blob and download
      return new Promise<boolean>((resolve) => {
        printCanvas.toBlob((blob) => {
          if (!blob) {
            console.error(`Failed to create blob for ${card.name}`);
            resolve(false);
            return;
          }
          
          console.log(`Blob created: ${blob.size} bytes for ${card.name}`);
          
          const url = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `${card.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_card.png`;
          link.style.display = 'none';
          
          // Force user interaction
          document.body.appendChild(link);
          
          // Add click listener to ensure download
          link.addEventListener('click', () => {
            console.log(`Download triggered for ${card.name}`);
            setTimeout(() => {
              URL.revokeObjectURL(url);
              if (document.body.contains(link)) {
                document.body.removeChild(link);
              }
            }, 100);
          });
          
          link.click();
          resolve(true);
        }, 'image/png'); // Remove compression for maximum quality
      }).finally(() => {
        // Cleanup
        if (document.body.contains(tempContainer)) {
          document.body.removeChild(tempContainer);
        }
      });
      
    } catch (error) {
      console.error(`Error downloading card ${card.name}:`, error);
      return false;
    }
  };

  const downloadCards = async () => {
    const cardsToDownload = cards.filter(card => selectedCards.has(card.id));
    
    if (cardsToDownload.length === 0) {
      toast({
        title: "No Cards Selected",
        description: "Please select cards to download",
        variant: "destructive",
      });
      return;
    }

    console.log(`Starting download of ${cardsToDownload.length} cards`);
    setLoading(true);
    setDownloadProgress({ current: 0, total: cardsToDownload.length });
    
    let successCount = 0;
    let failCount = 0;
    
    try {
      // Download cards with delay between each
      for (let i = 0; i < cardsToDownload.length; i++) {
        const card = cardsToDownload[i];
        console.log(`Processing card ${i + 1}/${cardsToDownload.length}: ${card.name}`);
        
        const success = await downloadSingleCard(card, i, cardsToDownload.length);
        
        if (success) {
          successCount++;
          console.log(`Successfully downloaded: ${card.name}`);
        } else {
          failCount++;
          console.error(`Failed to download: ${card.name}`);
        }
        
        // Delay between downloads to prevent browser throttling
        if (i < cardsToDownload.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
      
      // Show final result
      if (successCount > 0) {
        toast({
          title: "Download Complete",
          description: `Successfully downloaded ${successCount} card(s)` + 
                      (failCount > 0 ? `. ${failCount} failed.` : ''),
          variant: successCount === cardsToDownload.length ? "default" : "destructive"
        });
      } else {
        toast({
          title: "Download Failed",
          description: "No cards were downloaded successfully",
          variant: "destructive",
        });
      }
      
    } catch (error) {
      console.error('Download process error:', error);
      toast({
        title: "Download Error",
        description: "An unexpected error occurred during download",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
      setDownloadProgress(null);
    }
  };

  const downloadSingleCardFromTable = async (card: CardData) => {
    setLoading(true);
    setDownloadProgress({ current: 1, total: 1 });
    
    try {
      const success = await downloadSingleCard(card, 0, 1);
      
      if (success) {
        toast({
          title: "Download Complete",
          description: `Successfully downloaded ${card.name}`,
        });
      } else {
        toast({
          title: "Download Failed",
          description: `Failed to download ${card.name}`,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Download error:', error);
      toast({
        title: "Download Error",
        description: "An unexpected error occurred during download",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
      setDownloadProgress(null);
    }
  };

  const showPreview = async (card: CardData) => {
    setPreviewCard(card);
  };

  const handleDeleteCard = async (card: CardData) => {
    const confirmMessage = `Are you sure you want to delete "${card.name}"?\n\nThis action cannot be undone and will:\n- Remove the card from the ${card.source} table\n- Delete the image from storage\n- Remove all associated likes and data`;
    
    if (!confirm(confirmMessage)) {
      return;
    }

    try {
      setLoading(true);

      // Delete from the appropriate table
      const tableName = card.source === 'leaderboard' ? 'leaderboard_cards' : 'user_cards';
      const { error: deleteError } = await supabase
        .from(tableName)
        .delete()
        .eq('id', card.id);

      if (deleteError) throw deleteError;

      // Delete associated session likes
      const { error: sessionLikesError } = await supabase
        .from('session_likes')
        .delete()
        .eq('card_id', card.id);

      // Delete associated user likes (if any)
      const { error: userLikesError } = await supabase
        .from('user_likes')
        .delete()
        .eq('card_id', card.id);

      // Try to delete image from storage if it's in the character-images bucket
      if (card.image_url && card.image_url.includes('character-images')) {
        const imagePath = card.image_url.split('/character-images/')[1];
        if (imagePath) {
          const { error: storageError } = await supabase.storage
            .from('character-images')
            .remove([imagePath]);
          
          if (storageError) {
            console.warn('Failed to delete image from storage:', storageError);
          }
        }
      }

      toast({
        title: "Card Deleted",
        description: `Successfully deleted "${card.name}" from ${card.source} cards`,
      });

      // Refresh data
      loadCards();
      loadAllCards();
      
    } catch (error) {
      console.error('Error deleting card:', error);
      toast({
        title: "Delete Failed",
        description: "Failed to delete the card. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Prompt management functions
  const loadPrompts = async () => {
    try {
      const { data, error } = await supabase
        .from('admin_prompts')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setPrompts(data || []);
    } catch (error) {
      console.error('Error loading prompts:', error);
      toast({
        title: "Error",
        description: "Failed to load prompts",
        variant: "destructive",
      });
    }
  };

  const handleAddPrompt = async () => {
    if (!newPrompt.name.trim() || !newPrompt.prompt_text.trim()) {
      toast({
        title: "Validation Error",
        description: "Name and prompt text are required",
        variant: "destructive",
      });
      return;
    }

    try {
      const { error } = await supabase
        .from('admin_prompts')
        .insert([newPrompt]);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Prompt added successfully",
      });

      setNewPrompt({ name: '', prompt_text: '', is_active: true });
      setIsAddingPrompt(false);
      loadPrompts();
    } catch (error) {
      console.error('Error adding prompt:', error);
      toast({
        title: "Error",
        description: "Failed to add prompt",
        variant: "destructive",
      });
    }
  };

  const handleUpdatePrompt = async (prompt: AdminPrompt) => {
    if (!prompt.name.trim() || !prompt.prompt_text.trim()) {
      toast({
        title: "Validation Error",
        description: "Name and prompt text are required",
        variant: "destructive",
      });
      return;
    }

    try {
      const { error } = await supabase
        .from('admin_prompts')
        .update({
          name: prompt.name,
          prompt_text: prompt.prompt_text,
          is_active: prompt.is_active,
          updated_at: new Date().toISOString()
        })
        .eq('id', prompt.id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Prompt updated successfully",
      });

      setEditingPrompt(null);
      loadPrompts();
    } catch (error) {
      console.error('Error updating prompt:', error);
      toast({
        title: "Error",
        description: "Failed to update prompt",
        variant: "destructive",
      });
    }
  };

  const handleDeletePrompt = async (promptId: string) => {
    if (!confirm('Are you sure you want to delete this prompt?')) {
      return;
    }

    try {
      const { error } = await supabase
        .from('admin_prompts')
        .delete()
        .eq('id', promptId);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Prompt deleted successfully",
      });

      loadPrompts();
    } catch (error) {
      console.error('Error deleting prompt:', error);
      toast({
        title: "Error",
        description: "Failed to delete prompt",
        variant: "destructive",
      });
    }
  };

  const handleTogglePromptStatus = async (prompt: AdminPrompt) => {
    try {
      const { error } = await supabase
        .from('admin_prompts')
        .update({ 
          is_active: !prompt.is_active,
          updated_at: new Date().toISOString()
        })
        .eq('id', prompt.id);

      if (error) throw error;

      toast({
        title: "Success",
        description: `Prompt ${!prompt.is_active ? 'activated' : 'deactivated'} successfully`,
      });

      loadPrompts();
    } catch (error) {
      console.error('Error toggling prompt status:', error);
      toast({
        title: "Error",
        description: "Failed to update prompt status",
        variant: "destructive",
      });
    }
  };

  // Load prompt history
  const loadPromptHistory = async () => {
    try {
      const { data, error } = await supabase
        .from('prompt_history')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setPromptHistory(data || []);
    } catch (error) {
      console.error('Error loading prompt history:', error);
      toast({
        title: "Error",
        description: "Failed to load prompt history",
        variant: "destructive",
      });
    }
  };

  // Filter prompts based on search term
  const filteredPrompts = prompts.filter(prompt => 
    prompt.name.toLowerCase().includes(promptSearchTerm.toLowerCase()) ||
    prompt.prompt_text.toLowerCase().includes(promptSearchTerm.toLowerCase())
  );

  // Filter prompt history based on search term
  const filteredPromptHistory = promptHistory.filter(history => 
    history.user_prompt.toLowerCase().includes(historySearchTerm.toLowerCase()) ||
    history.processed_prompt.toLowerCase().includes(historySearchTerm.toLowerCase()) ||
    (history.dalle_revised_prompt && history.dalle_revised_prompt.toLowerCase().includes(historySearchTerm.toLowerCase())) ||
    history.generation_status.toLowerCase().includes(historySearchTerm.toLowerCase())
  );


  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-background/80 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Admin Access</CardTitle>
            <CardDescription>Enter the admin password to manage prompts</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handlePasswordSubmit} className="space-y-4">
              <Input
                type="password"
                placeholder="Admin password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full"
              />
              <Button type="submit" className="w-full">
                Access Admin Panel
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate('/')}
                className="w-full"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Home
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-background/80 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-3xl font-bold text-foreground">Admin Panel</h1>
          <Button variant="outline" onClick={() => navigate('/')}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Button>
        </div>

        <div className="space-y-6">
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="cards">Card Creations</TabsTrigger>
              <TabsTrigger value="orders">Orders</TabsTrigger>
              <TabsTrigger value="prompts">DALL-E Prompts</TabsTrigger>
              <TabsTrigger value="history">Prompt History</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>
            
            <TabsContent value="overview" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Cards</CardTitle>
                    <Database className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{allCards.length}</div>
                    <p className="text-xs text-muted-foreground">
                      {allCards.filter(c => c.source === 'leaderboard').length} leaderboard, {allCards.filter(c => c.source === 'user').length} user cards
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
                    <ShoppingCart className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{orders.length}</div>
                    <p className="text-xs text-muted-foreground">
                      {orders.filter(o => o.status === 'pending').length} pending
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                    <ShoppingCart className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      ${orders.reduce((sum, order) => sum + (order.total_amount || 0), 0).toFixed(2)}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      From {orders.length} orders
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* Admin Tools Section */}
              <Card>
                <CardHeader>
                  <CardTitle>Admin Tools</CardTitle>
                  <CardDescription>
                    Management tools for maintaining the application
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <UpdateDescriptors />
                  <GenerateStats />
                  <UpdateStories />
                </CardContent>
              </Card>
              
            </TabsContent>

            <TabsContent value="cards" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>All Card Creations</CardTitle>
                  <CardDescription>
                    Complete overview of all cards created in the system
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                      <Input
                        placeholder="Search cards by name, description, or creator..."
                        value={cardSearchTerm}
                        onChange={(e) => setCardSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                    
                    <div className="rounded-md border max-h-96 overflow-y-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Image</TableHead>
                            <TableHead>Name</TableHead>
                            <TableHead>Creator</TableHead>
                            <TableHead>Description</TableHead>
                            <TableHead>Level</TableHead>
                            <TableHead>Likes</TableHead>
                             <TableHead>Source</TableHead>
                             <TableHead>Created</TableHead>
                             <TableHead>Actions</TableHead>
                           </TableRow>
                         </TableHeader>
                         <TableBody>
                           {filteredAllCards.map((card) => (
                             <TableRow key={`${card.source}-${card.id}`}>
                               <TableCell>
                                 <img 
                                   src={card.image_url} 
                                   alt={card.name}
                                   className="w-12 h-12 object-cover rounded"
                                 />
                               </TableCell>
                               <TableCell className="font-medium">{card.name}</TableCell>
                                <TableCell>
                                  <span className="text-sm text-muted-foreground">
                                    {formatCreator(card.signature)}
                                  </span>
                               </TableCell>
                              <TableCell className="max-w-xs">
                                <div className="truncate">
                                  {card.backstory || 'No description'}
                                </div>
                              </TableCell>
                              <TableCell>{card.level}</TableCell>
                              <TableCell>{card.likes || 0}</TableCell>
                              <TableCell>
                                <span className={`px-2 py-1 rounded-full text-xs ${
                                  card.source === 'leaderboard' 
                                    ? 'bg-green-100 text-green-800' 
                                    : 'bg-blue-100 text-blue-800'
                                }`}>
                                  {card.source}
                                </span>
                              </TableCell>
                               <TableCell>
                                 {new Date(card.created_at).toLocaleDateString()} {new Date(card.created_at).toLocaleTimeString()}
                               </TableCell>
                                <TableCell>
                                  <div className="flex gap-2">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => showPreview(card)}
                                      className="flex items-center gap-1"
                                    >
                                      <Eye className="w-3 h-3" />
                                      Preview
                                    </Button>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => downloadSingleCardFromTable(card)}
                                      className="flex items-center gap-1"
                                    >
                                      <Download className="w-3 h-3" />
                                      Download
                                    </Button>
                                    <Button
                                      variant="destructive"
                                      size="sm"
                                      onClick={() => handleDeleteCard(card)}
                                      className="flex items-center gap-1"
                                    >
                                      <Trash2 className="w-3 h-3" />
                                      Delete
                                    </Button>
                                  </div>
                                </TableCell>
                             </TableRow>
                           ))}
                         </TableBody>
                       </Table>
                     </div>
                    
                    {filteredAllCards.length === 0 && (
                      <div className="text-center py-8 text-muted-foreground">
                        No cards found matching your search.
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="orders" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>All Orders</CardTitle>
                  <CardDescription>
                    Complete overview of all orders placed
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                      <Input
                        placeholder="Search orders by customer name, email, or status..."
                        value={orderSearchTerm}
                        onChange={(e) => setOrderSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                    
                    <div className="rounded-md border max-h-96 overflow-y-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Order ID</TableHead>
                            <TableHead>Customer</TableHead>
                            <TableHead>Email</TableHead>
                            <TableHead>Address</TableHead>
                            <TableHead>Phone</TableHead>
                            <TableHead>Items</TableHead>
                            <TableHead>Quantity</TableHead>
                            <TableHead>Amount</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Created</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {filteredOrders.map((order) => (
                            <TableRow key={order.id}>
                              <TableCell className="font-mono text-xs">
                                {order.id.slice(0, 8)}...
                              </TableCell>
                              <TableCell className="font-medium">{order.customer_name}</TableCell>
                              <TableCell>{order.customer_email}</TableCell>
                              <TableCell className="max-w-xs">
                                <div className="truncate">
                                  {order.customer_address}, {order.customer_city}, {order.customer_state} {order.customer_zip}
                                </div>
                              </TableCell>
                              <TableCell>{order.customer_phone || 'N/A'}</TableCell>
                              <TableCell>
                                <div className="max-w-xs">
                                  <div className="truncate">
                                    {Array.isArray(order.order_items) 
                                      ? order.order_items.map((item: any) => `${item.quantity}x ${item.name || 'Card'}`).join(', ')
                                      : 'Order items'
                                    }
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell>{order.total_quantity}</TableCell>
                              <TableCell>${order.total_amount?.toFixed(2) || '0.00'}</TableCell>
                              <TableCell>
                                <span className={`px-2 py-1 rounded-full text-xs ${
                                  order.status === 'pending' 
                                    ? 'bg-yellow-100 text-yellow-800' 
                                    : order.status === 'completed'
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-gray-100 text-gray-800'
                                }`}>
                                  {order.status}
                                </span>
                              </TableCell>
                              <TableCell>
                                {new Date(order.created_at).toLocaleDateString()} {new Date(order.created_at).toLocaleTimeString()}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                    
                    {filteredOrders.length === 0 && (
                      <div className="text-center py-8 text-muted-foreground">
                        No orders found matching your search.
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="prompts" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>DALL-E Prompt Management</CardTitle>
                  <CardDescription>
                    Manage prompt templates used for AI image generation
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Search and Add Button */}
                    <div className="flex gap-4">
                      <div className="relative flex-1">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                        <Input
                          placeholder="Search prompts by name or content..."
                          value={promptSearchTerm}
                          onChange={(e) => setPromptSearchTerm(e.target.value)}
                          className="pl-10"
                        />
                      </div>
                      <Button
                        onClick={() => setIsAddingPrompt(true)}
                        className="flex items-center gap-2"
                      >
                        <Plus className="w-4 h-4" />
                        Add Prompt
                      </Button>
                    </div>

                    {/* Add New Prompt Form */}
                    {isAddingPrompt && (
                      <Card className="border-dashed">
                        <CardHeader>
                          <CardTitle className="text-lg">Add New Prompt</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div>
                            <label className="text-sm font-medium">Name</label>
                            <Input
                              placeholder="Enter prompt name (e.g., baseline_character)"
                              value={newPrompt.name}
                              onChange={(e) => setNewPrompt({ ...newPrompt, name: e.target.value })}
                            />
                          </div>
                          <div>
                            <label className="text-sm font-medium">Prompt Text</label>
                            <textarea
                              placeholder="Enter the DALL-E prompt template..."
                              value={newPrompt.prompt_text}
                              onChange={(e) => setNewPrompt({ ...newPrompt, prompt_text: e.target.value })}
                              className="w-full min-h-[100px] px-3 py-2 text-sm border border-input bg-background rounded-md"
                            />
                          </div>
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              checked={newPrompt.is_active}
                              onCheckedChange={(checked) => setNewPrompt({ ...newPrompt, is_active: checked as boolean })}
                            />
                            <label className="text-sm">Active</label>
                          </div>
                          <div className="flex gap-2">
                            <Button onClick={handleAddPrompt}>
                              <Save className="w-4 h-4 mr-2" />
                              Save Prompt
                            </Button>
                            <Button
                              variant="outline"
                              onClick={() => {
                                setIsAddingPrompt(false);
                                setNewPrompt({ name: '', prompt_text: '', is_active: true });
                              }}
                            >
                              Cancel
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    )}

                    {/* Card Superpower Prompt Template */}
                    <Card className="border-blue-200 bg-blue-50/50">
                      <CardHeader>
                        <CardTitle className="text-lg flex items-center gap-2">
                          <AlertCircle className="w-5 h-5 text-blue-600" />
                          Card Superpower Prompt Template
                        </CardTitle>
                        <CardDescription>
                          This prompt template is used to generate character superpowers for cards. It's hardcoded in the generate-story and update-long-descriptors edge functions.
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div>
                          <label className="text-sm font-medium text-blue-700">System Prompt</label>
                          <div className="mt-1 p-3 bg-white border border-blue-200 rounded-md text-sm font-mono">
                            {descriptorPromptTemplate.systemPrompt}
                          </div>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-blue-700">User Prompt Template</label>
                          <div className="mt-1 p-3 bg-white border border-blue-200 rounded-md text-sm font-mono whitespace-pre-wrap">
                            {descriptorPromptTemplate.userPrompt}
                          </div>
                        </div>
                        <div className="text-xs text-blue-600 bg-blue-100 p-2 rounded border">
                          <strong>Note:</strong> To modify this prompt, you need to update the edge functions: generate-story and update-long-descriptors
                        </div>
                      </CardContent>
                    </Card>

                    {/* Character Stats Generation Prompt Template */}
                    <Card className="border-green-200 bg-green-50/50">
                      <CardHeader>
                        <CardTitle className="text-lg flex items-center gap-2">
                          <AlertCircle className="w-5 h-5 text-green-600" />
                          Character Stats Generation Prompt Template
                        </CardTitle>
                        <CardDescription>
                          This prompt template is used to generate balanced character stats (Power, Magic, Speed) for cards. It's hardcoded in the generate-character-stats edge function.
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div>
                          <label className="text-sm font-medium text-green-700">Prompt Template</label>
                          <div className="mt-1 p-3 bg-white border border-green-200 rounded-md text-sm font-mono whitespace-pre-wrap">
                            {statsPromptTemplate}
                          </div>
                        </div>
                        <div className="text-xs text-green-600 bg-green-100 p-2 rounded border">
                          <strong>Note:</strong> To modify this prompt, you need to update the generate-character-stats edge function. Stats must always have one strong (70-90), one moderate (40-70), and one weak (20-50) stat for balance.
                        </div>
                      </CardContent>
                    </Card>

                    {/* Prompts List */}
                    <div className="space-y-4 max-h-96 overflow-y-auto">
                      {filteredPrompts.map((prompt) => (
                        <Card key={prompt.id} className={`${!prompt.is_active ? 'opacity-60' : ''}`}>
                          <CardContent className="p-4">
                            {editingPrompt?.id === prompt.id ? (
                              /* Edit Mode */
                              <div className="space-y-4">
                                <div>
                                  <label className="text-sm font-medium">Name</label>
                                  <Input
                                    value={editingPrompt.name}
                                    onChange={(e) => setEditingPrompt({ ...editingPrompt, name: e.target.value })}
                                  />
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Prompt Text</label>
                                  <textarea
                                    value={editingPrompt.prompt_text}
                                    onChange={(e) => setEditingPrompt({ ...editingPrompt, prompt_text: e.target.value })}
                                    className="w-full min-h-[100px] px-3 py-2 text-sm border border-input bg-background rounded-md"
                                  />
                                </div>
                                <div className="flex items-center space-x-2">
                                  <Checkbox
                                    checked={editingPrompt.is_active}
                                    onCheckedChange={(checked) => setEditingPrompt({ ...editingPrompt, is_active: checked as boolean })}
                                  />
                                  <label className="text-sm">Active</label>
                                </div>
                                <div className="flex gap-2">
                                  <Button onClick={() => handleUpdatePrompt(editingPrompt)}>
                                    <Save className="w-4 h-4 mr-2" />
                                    Save
                                  </Button>
                                  <Button variant="outline" onClick={() => setEditingPrompt(null)}>
                                    Cancel
                                  </Button>
                                </div>
                              </div>
                            ) : (
                              /* View Mode */
                              <div className="space-y-3">
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center gap-2">
                                    <h4 className="font-semibold">{prompt.name}</h4>
                                    <span className={`px-2 py-1 rounded-full text-xs ${
                                      prompt.is_active 
                                        ? 'bg-green-100 text-green-800' 
                                        : 'bg-gray-100 text-gray-800'
                                    }`}>
                                      {prompt.is_active ? 'Active' : 'Inactive'}
                                    </span>
                                  </div>
                                  <div className="flex gap-2">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => handleTogglePromptStatus(prompt)}
                                    >
                                      {prompt.is_active ? 'Deactivate' : 'Activate'}
                                    </Button>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => setEditingPrompt(prompt)}
                                    >
                                      <Edit2 className="w-4 h-4" />
                                    </Button>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => handleDeletePrompt(prompt.id)}
                                      className="text-destructive hover:text-destructive"
                                    >
                                      <Trash2 className="w-4 h-4" />
                                    </Button>
                                  </div>
                                </div>
                                <div>
                                  <p className="text-sm text-muted-foreground bg-muted p-3 rounded-md whitespace-pre-wrap">
                                    {prompt.prompt_text}
                                  </p>
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  Updated: {new Date(prompt.updated_at).toLocaleDateString()} {new Date(prompt.updated_at).toLocaleTimeString()}
                                </div>
                              </div>
                            )}
                          </CardContent>
                        </Card>
                      ))}
                    </div>

                    {filteredPrompts.length === 0 && !isAddingPrompt && (
                      <div className="text-center py-8 text-muted-foreground">
                        <AlertCircle className="w-12 h-12 mx-auto mb-4 opacity-50" />
                        <p>No prompts found matching your search.</p>
                        {prompts.length === 0 && (
                          <p className="mt-2">Get started by adding your first prompt!</p>
                        )}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="history" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Prompt History</CardTitle>
                  <CardDescription>
                    Complete history of all prompts sent to DALL-E and their results
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                      <Input
                        placeholder="Search by user prompt, processed prompt, or status..."
                        value={historySearchTerm}
                        onChange={(e) => setHistorySearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                    
                    <div className="rounded-md border max-h-96 overflow-y-auto">
                      <Table>
                         <TableHeader>
                           <TableRow>
                             <TableHead>Status</TableHead>
                             <TableHead>User Prompt</TableHead>
                             <TableHead>Processed Prompt</TableHead>
                             <TableHead>DALL-E Revised</TableHead>
                             <TableHead>Image</TableHead>
                             <TableHead>Generation Time</TableHead>
                             <TableHead>Error</TableHead>
                             <TableHead>User</TableHead>
                             <TableHead>Created</TableHead>
                           </TableRow>
                         </TableHeader>
                         <TableBody>
                           {filteredPromptHistory.map((history) => (
                             <TableRow key={history.id} className="cursor-pointer hover:bg-muted/50" onClick={() => setSelectedHistoryItem(history)}>
                               <TableCell>
                                 <span className={`px-2 py-1 rounded-full text-xs ${
                                   history.generation_status === 'success' 
                                     ? 'bg-green-100 text-green-800' 
                                     : history.generation_status === 'error'
                                     ? 'bg-red-100 text-red-800'
                                     : history.generation_status === 'processing'
                                     ? 'bg-blue-100 text-blue-800'
                                     : 'bg-yellow-100 text-yellow-800'
                                 }`}>
                                   {history.generation_status}
                                 </span>
                               </TableCell>
                               <TableCell className="max-w-xs">
                                 <div className="truncate" title="Click to view full details">
                                   {history.user_prompt}
                                 </div>
                               </TableCell>
                               <TableCell className="max-w-xs">
                                 <div className="truncate" title="Click to view full details">
                                   {history.processed_prompt || 'Not processed'}
                                 </div>
                               </TableCell>
                               <TableCell className="max-w-xs">
                                 <div className="truncate" title="Click to view full details">
                                   {history.dalle_revised_prompt || 'N/A'}
                                 </div>
                               </TableCell>
                               <TableCell>
                                 {history.image_url ? (
                                   <img 
                                     src={history.image_url} 
                                     alt="Generated"
                                     className="w-12 h-12 object-cover rounded"
                                   />
                                 ) : (
                                   <span className="text-muted-foreground text-xs">No image</span>
                                 )}
                               </TableCell>
                               <TableCell>
                                 <span className="text-xs font-mono">
                                   {calculateGenerationTime(history)}
                                 </span>
                               </TableCell>
                               <TableCell className="max-w-xs">
                                 {history.error_message ? (
                                   <div className="truncate text-red-600 text-xs" title="Click to view full details">
                                     {history.error_message}
                                   </div>
                                 ) : (
                                   <span className="text-muted-foreground text-xs">-</span>
                                 )}
                               </TableCell>
                               <TableCell>
                                 <span className="text-xs text-muted-foreground">
                                   {history.user_id ? `User: ${history.user_id.slice(0, 8)}...` : 
                                    history.session_id ? `Session: ${history.session_id.slice(0, 8)}...` : 
                                    'Anonymous'}
                                 </span>
                               </TableCell>
                               <TableCell>
                                 <div className="text-xs">
                                   {new Date(history.created_at).toLocaleDateString()}
                                   <br />
                                   {new Date(history.created_at).toLocaleTimeString()}
                                 </div>
                               </TableCell>
                             </TableRow>
                           ))}
                         </TableBody>
                      </Table>
                    </div>
                    
                    {filteredPromptHistory.length === 0 && (
                      <div className="text-center py-8 text-muted-foreground">
                        <AlertCircle className="w-12 h-12 mx-auto mb-4 opacity-50" />
                        <p>No prompt history found matching your search.</p>
                        {promptHistory.length === 0 && (
                          <p className="mt-2">Prompt history will appear here once image generation starts.</p>
                        )}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="settings" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Image Generation Settings</CardTitle>
                    <CardDescription>
                      Control image quality for all DALL-E generations
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <label className="text-sm font-medium">Image Quality</label>
                      <div className="flex gap-2 mt-2">
                        <Button
                          variant={imageSettings?.dalle_settings?.quality === 'standard' ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => updateImageQuality('standard')}
                          disabled={!imageSettings}
                        >
                          Standard
                        </Button>
                        <Button
                          variant={imageSettings?.dalle_settings?.quality === 'hd' ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => updateImageQuality('hd')}
                          disabled={!imageSettings}
                        >
                          HD
                        </Button>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        Current: {imageSettings?.dalle_settings?.quality || 'Loading...'}
                      </p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Bulk Operations</CardTitle>
                    <CardDescription>
                      Administrative tools for bulk card operations
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <UpdateStories />
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
      
      {/* Preview Modal */}
      {previewCard && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-7xl max-h-[98vh] overflow-auto relative">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPreviewCard(null)}
              className="absolute top-4 right-4 z-10 bg-white/90 hover:bg-white"
            >
              <X className="w-4 h-4" />
            </Button>
            <div className="p-8">
              <h3 className="text-lg font-medium mb-4 text-center">Preview: {previewCard.name}</h3>
              <div 
                dangerouslySetInnerHTML={{ 
                  __html: (() => {
                    // Always prefer backstory; ignore legacy image_description
                    const rawDescription = (previewCard.backstory && previewCard.backstory.trim().length > 0) ? previewCard.backstory : '';
                    const oneLineDescription = toOneLineDescription(rawDescription, 60);
                    
                     // Use the same HTML generation function as download
                     return createPrintCardHTML({
                      imageUrl: previewCard.image_url,
                      name: previewCard.name,
                      description: oneLineDescription,
                      creator: previewCard.signature || '',
                      likes: previewCard.likes,
                      powerStat: previewCard.power_stat,
                      magicStat: previewCard.magic_stat,
                      speedStat: previewCard.speed_stat
                    });
                  })()
                }}
              />
            </div>
          </div>
        </div>
      )}
      
      {/* Prompt History Detail Modal */}
      {selectedHistoryItem && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-4xl max-h-[90vh] overflow-auto relative w-full">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSelectedHistoryItem(null)}
              className="absolute top-4 right-4 z-10 bg-white/90 hover:bg-white"
            >
              <X className="w-4 h-4" />
            </Button>
            <div className="p-6 space-y-6">
              <div className="flex items-center gap-4">
                <h3 className="text-lg font-medium">Prompt History Details</h3>
                <span className={`px-2 py-1 rounded-full text-xs ${
                  selectedHistoryItem.generation_status === 'success' 
                    ? 'bg-green-100 text-green-800' 
                    : selectedHistoryItem.generation_status === 'error'
                    ? 'bg-red-100 text-red-800'
                    : selectedHistoryItem.generation_status === 'processing'
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {selectedHistoryItem.generation_status}
                </span>
                <span className="text-sm text-muted-foreground font-mono">
                  Generation Time: {calculateGenerationTime(selectedHistoryItem)}
                </span>
              </div>
              
              <div className="grid gap-4">
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground mb-2">User Prompt</h4>
                  <p className="text-sm bg-muted p-3 rounded-md whitespace-pre-wrap">{selectedHistoryItem.user_prompt}</p>
                </div>
                
                {selectedHistoryItem.processed_prompt && (
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground mb-2">Processed Prompt</h4>
                    <p className="text-sm bg-muted p-3 rounded-md whitespace-pre-wrap">{selectedHistoryItem.processed_prompt}</p>
                  </div>
                )}
                
                {selectedHistoryItem.dalle_revised_prompt && (
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground mb-2">DALL-E Revised Prompt</h4>
                    <p className="text-sm bg-muted p-3 rounded-md whitespace-pre-wrap">{selectedHistoryItem.dalle_revised_prompt}</p>
                  </div>
                )}
                
                {selectedHistoryItem.error_message && (
                  <div>
                    <h4 className="font-medium text-sm text-red-600 mb-2">Error Message</h4>
                    <p className="text-sm bg-red-50 text-red-800 p-3 rounded-md whitespace-pre-wrap">{selectedHistoryItem.error_message}</p>
                  </div>
                )}
                
                {selectedHistoryItem.image_url && (
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground mb-2">Generated Image</h4>
                    <img 
                      src={selectedHistoryItem.image_url} 
                      alt="Generated"
                      className="max-w-full h-auto rounded-lg border"
                    />
                  </div>
                )}
                
                <div className="grid grid-cols-2 gap-4 text-xs text-muted-foreground bg-muted p-3 rounded">
                  <div>
                    <strong>User:</strong> {selectedHistoryItem.user_id ? `${selectedHistoryItem.user_id}` : 
                     selectedHistoryItem.session_id ? `Session: ${selectedHistoryItem.session_id}` : 
                     'Anonymous'}
                  </div>
                  <div>
                    <strong>Card ID:</strong> {selectedHistoryItem.card_id || 'N/A'}
                  </div>
                  <div>
                    <strong>Created:</strong> {new Date(selectedHistoryItem.created_at).toLocaleString()}
                  </div>
                  <div>
                    <strong>Updated:</strong> {new Date(selectedHistoryItem.updated_at).toLocaleString()}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Admin;