import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { PixiCardCarousel } from "@/components/PixiCardCarousel";
import { ImageGenerator } from "@/components/ImageGenerator";
import { CharacterCard, CardData } from "@/components/CharacterCard";
import { Leaderboard } from "@/components/Leaderboard";
import { ShippingForm } from "@/components/ShippingForm";
import { ImageMigration } from "@/components/ImageMigration";
import { Home, Trophy, Plus, Heart } from "lucide-react";
import { PlayingCardStar } from "@/components/ui/playing-card-star";
import { PromptCarousel } from "@/components/PromptCarousel";

import { ChatInterface } from "@/components/ChatInterface";
import confetti from "canvas-confetti";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

type Page = 'home' | 'create' | 'leaderboard' | 'collection' | 'print';

const Index = () => {
  const navigate = useNavigate();
  
  const [currentPage, setCurrentPage] = useState<Page>('home');
  const [generatedImage, setGeneratedImage] = useState<string>("");
  const [currentPrompt, setCurrentPrompt] = useState<string>("");
  const [currentSignature, setCurrentSignature] = useState<string>("");
  const [currentImageDescription, setCurrentImageDescription] = useState<string>("");
  const [sessionId, setSessionId] = useState<string>("");
  const [savedCard, setSavedCard] = useState<CardData | null>(null);
  const [isCurrentCardSaved, setIsCurrentCardSaved] = useState<boolean>(false);
  const [showEditConfirmation, setShowEditConfirmation] = useState(true);
  const [userWantsToEdit, setUserWantsToEdit] = useState(false);
  const [isProcessingEdit, setIsProcessingEdit] = useState(false);
  const [editHistory, setEditHistory] = useState<Array<{
    imageUrl: string;
    instruction: string;
    timestamp: Date;
  }>>([]);
  const [originalImage, setOriginalImage] = useState<string>("");
  const [originalPrompt, setOriginalPrompt] = useState<string>("");
  
  const { toast } = useToast();

  // Generate or retrieve session ID for persistence
  useEffect(() => {
    let id = localStorage.getItem('magic-cards-session');
    if (!id) {
      id = crypto.randomUUID();
      localStorage.setItem('magic-cards-session', id);
    }
    setSessionId(id);
  }, []);

  const handleImageGenerated = (imageUrl: string, prompt: string, signature?: string, imageDescription?: string) => {
    console.log('🎯 Image generated, updating state:', { imageUrl, prompt, signature, imageDescription });
    setGeneratedImage(imageUrl);
    setCurrentPrompt(prompt);
    setCurrentSignature(signature || "");
    setCurrentImageDescription(imageDescription || "");
    setIsCurrentCardSaved(false); // Reset save status for new image
    setShowEditConfirmation(true); // Always show confirmation for new image
    setUserWantsToEdit(false); // Reset edit state to start fresh
    setSavedCard(null); // Clear any previously saved card
    
    // Initialize edit tracking for new images
    setOriginalImage(imageUrl);
    setOriginalPrompt(prompt);
    setEditHistory([]);
    
    setCurrentPage('create');
  };

  const handleCardSaved = async (cardData: CardData) => {
    try {
      // Require authentication before saving
      const { data: authData } = await supabase.auth.getSession();
      if (!authData?.session) {
        try { localStorage.setItem('postAuthRedirect', '/'); } catch {}
        toast({ title: 'Sign in required', description: 'Please sign in to save your card.' });
        navigate('/auth?redirect=/');
        return;
      }

      // Get the next level for this character
      const { data: levelData, error: levelError } = await supabase
        .rpc('get_next_character_level', { character_name: cardData.name });

      if (levelError) throw levelError;

      const nextLevel = levelData || 1;

      const { error } = await supabase
        .from('leaderboard_cards')
        .insert({
          name: cardData.name,
          backstory: cardData.backstory,
          signature: cardData.signature,
          image_url: cardData.imageUrl,
          image_description: currentImageDescription,
          level: nextLevel,
          power_stat: cardData.power_stat,
          magic_stat: cardData.magic_stat,
          speed_stat: cardData.speed_stat,
          user_id: authData.session.user.id
        });
      
      if (error) {
        console.error('Error saving card:', error);
        toast({
          title: "❌ Save Failed",
          description: "Failed to save your card. Please try again.",
          variant: "destructive"
        });
        return;
      }
      
      setSavedCard(cardData);
      setIsCurrentCardSaved(true); // Mark current card as saved
      
      // Show confetti
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 }
      });
      
      toast({
        title: "🎉 Card Saved to Leaderboard!",
        description: "Your character has been added to the leaderboard!",
      });
      
      // Navigate to leaderboard after saving
      setCurrentPage('leaderboard');
    } catch (error) {
      console.error('Error saving card:', error);
      toast({
        title: "❌ Save Failed",
        description: "Failed to save your card. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleStartCreating = (prompt?: string) => {
    // Reset all card creation state
    setGeneratedImage("");
    setSavedCard(null);
    setCurrentSignature("");
    setIsCurrentCardSaved(false); // Reset save status
    setEditHistory([]);
    setOriginalImage("");
    setOriginalPrompt("");
    
    if (prompt) {
      // Ensure prompt is always a string
      setCurrentPrompt(String(prompt));
    } else {
      setCurrentPrompt("");
    }
    setCurrentPage('create');
  };

  // Print functionality removed - users can order cards through the order page

  const renderPage = () => {
    switch (currentPage) {
      case 'home':
        return (
            <div className="space-y-8">
            {/* Hero Section */}
            <div className="text-center space-y-4">
              <div className="flex items-center justify-center mb-4">
                <PlayingCardStar className="w-8 h-8 sm:w-10 sm:h-10 text-accent mr-3 sm:mr-4 animate-sparkle" />
                <h1 className="text-3xl sm:text-5xl font-bold bg-hero-gradient bg-clip-text text-transparent">
                  PIXICARDS
                </h1>
                <PlayingCardStar className="w-8 h-8 sm:w-10 sm:h-10 text-accent ml-3 sm:ml-4 animate-sparkle" />
              </div>
              <p className="text-lg sm:text-xl text-muted-foreground max-w-3xl mx-auto px-4 leading-relaxed">
                Create your own PixiCards with AI magic! Vote on the top stars every week and win prizes.
              </p>
            </div>

            {/* Image Migration */}
            <ImageMigration />

            {/* PixiCard Carousel */}
            <PixiCardCarousel onCreateOwn={handleStartCreating} />

            {/* Action Buttons - Mobile Optimized */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center px-4">
              <Button 
                onClick={() => handleStartCreating()}
                className="bg-hero-gradient hover:scale-105 transition-bounce text-white font-bold px-6 py-3 rounded-full shadow-magical text-base w-full sm:w-auto min-h-[48px]"
              >
                <Plus className="w-5 h-5 mr-2" />
                Create Your Own
              </Button>
              <Button 
                variant="outline"
                onClick={() => setCurrentPage('leaderboard')}
                className="px-6 py-3 rounded-full hover:scale-105 transition-bounce text-base w-full sm:w-auto min-h-[48px] border-accent/30 hover:border-accent/50"
              >
                <Trophy className="w-5 h-5 mr-2" />
                <PlayingCardStar className="w-4 h-4 mr-1 text-yellow-500" />
                Star Leaderboard
              </Button>
            </div>
          </div>
        );

      case 'create':
        return (
          <div className="space-y-8">
            {!generatedImage && (
              <PromptCarousel onSelectPrompt={(prompt) => handleStartCreating(prompt)} />
            )}
            {!generatedImage ? (
              <ImageGenerator 
                onImageGenerated={handleImageGenerated}
                initialPrompt={currentPrompt}
              />
            ) : (
              <div className="flex flex-col lg:flex-row gap-6 lg:gap-8 items-start">
                <div className="w-full lg:flex-1">
                  <CharacterCard
                    imageUrl={generatedImage}
                    onSave={isCurrentCardSaved ? undefined : handleCardSaved}
                    onDelete={() => {
                      console.log('🗑️ Deleting card, resetting all state');
                      setGeneratedImage("");
                      setCurrentPrompt("");
                      setCurrentSignature("");
                      setCurrentImageDescription("");
                      setIsCurrentCardSaved(false);
                      setShowEditConfirmation(true);
                      setUserWantsToEdit(false);
                      setSavedCard(null);
                    }}
                    isEditable={!isCurrentCardSaved} // Disable editing after card is saved
                    isCurrentCardSaved={isCurrentCardSaved}
                    initialData={{ signature: currentSignature }}
                    imageDescription={currentImageDescription}
                    showDeleteButton={!isCurrentCardSaved} // Only show delete if not saved
                  />
                </div>
                <div className="w-full lg:flex-1 space-y-6 lg:space-y-8">
                  {/* Always show edit confirmation for new images, not saved cards */}
                  {!isCurrentCardSaved && !savedCard && showEditConfirmation && (
                    <div className="flex flex-col items-center justify-center p-6 sm:p-8 bg-gradient-to-br from-purple-50/50 to-pink-50/50 dark:from-purple-950/20 dark:to-pink-950/20 border-2 border-gradient-to-r from-purple-300 to-pink-300 dark:from-purple-700 dark:to-pink-700 rounded-2xl shadow-lg">
                      <div className="text-center mb-6">
                        <h3 className="text-lg sm:text-xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2">
                          Want to make changes? ✨
                        </h3>
                        <p className="text-sm sm:text-base text-muted-foreground">
                          You can edit your character to make it even more magical!
                        </p>
                      </div>
                      <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 w-full max-w-md">
                        <button
                          onClick={() => {
                            console.log('🎨 User wants to edit, showing chat interface');
                            setUserWantsToEdit(true);
                            setShowEditConfirmation(false);
                          }}
                          className="px-4 sm:px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white rounded-xl font-medium shadow-lg transition-all min-h-[48px] text-sm sm:text-base"
                        >
                          Yes, let's edit! 🎨
                        </button>
                        <button
                          onClick={() => {
                            console.log('✅ User happy with card, hiding confirmation');
                            setShowEditConfirmation(false);
                          }}
                          className="px-4 sm:px-6 py-3 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-xl font-medium transition-all min-h-[48px] text-sm sm:text-base"
                        >
                          No, I'm happy with it
                        </button>
                      </div>
                    </div>
                  )}
                  
                   {/* Show chat interface only when user wants to edit and hasn't saved yet */}
                  {!isCurrentCardSaved && !savedCard && userWantsToEdit && !showEditConfirmation && (
                     <ChatInterface
                       onEditRequest={async (editInstructions: string, baseImageUrl: string, originalContext: string) => {
                         setIsProcessingEdit(true);
                         
                         try {
                           const { data, error } = await supabase.functions.invoke('generate-image', {
                             body: { 
                               mode: 'edit',
                               baseImageUrl: baseImageUrl,
                               editInstructions: editInstructions,
                               originalPrompt: originalContext
                             }
                           });

                           if (error) {
                             throw new Error(error.message);
                           }

                           if (data.error) {
                             throw new Error(data.error);
                           }

                           // Add to edit history
                           setEditHistory(prev => [...prev, {
                             imageUrl: generatedImage, // Previous image
                             instruction: editInstructions,
                             timestamp: new Date()
                           }]);

                           // Update current image
                           setGeneratedImage(data.imageUrl);
                           
                           toast({
                             title: "✨ Character Edited!",
                             description: "Your character has been updated with your changes!",
                           });
                         } catch (error) {
                           console.error('Error editing image:', error);
                           toast({
                             title: "❌ Edit Failed",
                             description: "Something went wrong while editing your character. Please try again!",
                             variant: "destructive"
                           });
                         } finally {
                           setIsProcessingEdit(false);
                         }
                       }}
                       initialMessage="edit"
                       isProcessing={isProcessingEdit}
                       currentImageUrl={generatedImage}
                       originalPrompt={originalPrompt}
                       editHistory={editHistory}
                     />
                  )}
                </div>
              </div>
            )}
          </div>
        );

      case 'print':
        return savedCard ? (
          <div className="space-y-8">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-foreground mb-2">Print Your PixiCard</h2>
              <p className="text-muted-foreground">Get a physical copy of your magical creation!</p>
            </div>
            <ShippingForm cardData={savedCard} />
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-muted-foreground">No card selected for printing.</p>
            <Button onClick={() => setCurrentPage('home')} className="mt-4">
              Go Home
            </Button>
          </div>
        );

      case 'leaderboard':
        return <Leaderboard />;
      case 'collection':
        return <Leaderboard mode="collection" />;


      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Navigation - Mobile Optimized */}
      <nav className="sticky top-0 z-50 bg-background/95 backdrop-blur-lg border-b border-border shadow-lg">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 py-3 sm:py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <PlayingCardStar className="w-6 h-6 sm:w-8 sm:h-8 text-accent mr-2 sm:mr-3" />
              <span className="text-lg sm:text-2xl font-bold bg-hero-gradient bg-clip-text text-transparent">PIXICARDS</span>
            </div>
            
            <div className="flex gap-2 sm:gap-3 items-center">
              {[
                { page: 'home' as Page, icon: Home, label: 'Home' },
                { page: 'create' as Page, icon: Plus, label: 'Create' },
                { page: 'leaderboard' as Page, icon: Trophy, label: 'Vote' }
              ].map(({ page, icon: Icon, label }) => (
                <Button
                  key={page}
                  variant={currentPage === page ? "default" : "outline"}
                  onClick={() => page === 'create' ? handleStartCreating() : setCurrentPage(page)}
                  className={`rounded-full transition-bounce text-xs sm:text-sm px-3 py-2 sm:px-4 sm:py-2 min-h-[40px] min-w-[40px] ${
                    currentPage === page 
                      ? 'bg-hero-gradient text-white' 
                      : 'hover:scale-105'
                  }`}
                >
                  <Icon className="w-3 h-3 sm:w-4 sm:h-4 sm:mr-1" />
                  <span className="hidden sm:inline">{label}</span>
                </Button>
              ))}
              
              <Button
                variant={currentPage === 'collection' ? "default" : "outline"}
                onClick={() => setCurrentPage('collection')}
                className={`rounded-full transition-bounce text-xs sm:text-sm px-3 py-2 sm:px-4 sm:py-2 min-h-[40px] min-w-[40px] ${
                  currentPage === 'collection' 
                    ? 'bg-hero-gradient text-white' 
                    : 'hover:scale-105'
                }`}
              >
                <Heart className="w-3 h-3 sm:w-4 sm:h-4 sm:mr-1" />
                <span className="hidden sm:inline">My Collection</span>
              </Button>

              <Button
                variant="default"
                onClick={() => navigate('/order')}
                className="rounded-full text-xs sm:text-sm px-3 py-2 sm:px-4 sm:py-2 min-h-[40px] bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white shadow-lg hover:scale-105 transition-all"
              >
                <span className="hidden sm:inline">Order</span>
                <span className="sm:hidden">🛒</span>
              </Button>
          </div>
        </div>
        </div>
      </nav>

      {/* Main Content - Mobile Optimized */}
      <main className="max-w-6xl mx-auto px-4 sm:px-6 py-4 sm:py-6">
        {renderPage()}
      </main>

      {/* Footer */}
      <footer className="text-center py-6">
        <p className="text-xs text-muted-foreground">
          Contact <NAME_EMAIL>
        </p>
      </footer>

      {/* Background Elements */}
      <div className="fixed inset-0 -z-10 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 sm:w-72 sm:h-72 bg-primary/5 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 sm:w-96 sm:h-96 bg-accent/5 rounded-full blur-3xl animate-bounce-gentle"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 sm:w-[800px] sm:h-[800px] bg-secondary/3 rounded-full blur-3xl animate-pulse"></div>
      </div>
    </div>
  );
};

export default Index;
