import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const openAIApiKey = Deno.env.get('OPENAI_API_KEY');

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

const FALLBACK_STATS = {
  power: 60,
  magic: 50,
  speed: 40
};

function validateStatBalance(power: number, magic: number, speed: number): boolean {
  const stats = [power, magic, speed];

  const isStrong = (v: number) => v >= 75 && v <= 90;
  const isModerate = (v: number) => v >= 45 && v <= 65;
  const isWeak = (v: number) => v >= 25 && v < 45; // exclusive upper bound to avoid overlap
  
  const strongCount = stats.filter(isStrong).length;
  const moderateCount = stats.filter(isModerate).length;
  const weakCount = stats.filter(isWeak).length;

  const spreadOK = Math.max(...stats) - Math.min(...stats) >= 20;
  
  return strongCount === 1 && moderateCount === 1 && weakCount === 1 && spreadOK;
}

async function generateStats(characterName: string, imageDescription: string, superpower: string, attempt: number = 1): Promise<{power: number, magic: number, speed: number}> {
  const prompt = `You are generating balanced character stats for a trading card game. 

Character: ${characterName}
Appearance: ${imageDescription}
Superpower: "${superpower}"

STEP 1: Analyze the superpower to identify which stat should be STRONG:
- If superpower mentions physical strength, fighting, muscles, armor, toughness → Power is STRONG
- If superpower mentions magic, spells, supernatural abilities, special powers, creativity → Magic is STRONG  
- If superpower mentions speed, flying, quick movement, agility, reactions → Speed is STRONG

STEP 2: Generate stats with this EXACT distribution:
- STRONG stat (matches superpower): 75-90
- MODERATE stat (somewhat relevant): 45-65
- WEAK stat (least relevant): 25-44

STEP 3: Ensure good spread - stats should be at least 20 points apart from each other.

Examples:
- "Flies at rainbow speed!" → Speed=85, Magic=55, Power=30
- "Casts ice magic spells!" → Magic=88, Power=50, Speed=30
- "Super strong muscle power!" → Power=82, Speed=55, Magic=35

Return ONLY this JSON format:
{"power": [number], "magic": [number], "speed": [number]}`;

  try {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        response_format: { type: 'json_object' },
        messages: [
          { role: 'system', content: 'You are a helpful assistant that returns strict JSON only.' },
          { role: 'user', content: prompt }
        ],
        temperature: 0.4,
        max_tokens: 120,
      }),
    });

    const data = await response.json();
    const content = data?.choices?.[0]?.message?.content?.trim() || '';
    
    console.log(`Stats generation attempt ${attempt}, response:`, content);
    
    // Parse JSON response, fallback to regex if needed
    let stats: any;
    try {
      stats = JSON.parse(content);
    } catch {
      const statsMatch = content.match(/\{[^}]*\}/);
      if (!statsMatch) {
        throw new Error('No JSON object found in response');
      }
      stats = JSON.parse(statsMatch[0]);
    }
    
    // Validate the stats structure and ranges
    if (!stats.power || !stats.magic || !stats.speed) {
      throw new Error('Missing required stat properties');
    }
    
    const power = Math.round(Number(stats.power));
    const magic = Math.round(Number(stats.magic));
    const speed = Math.round(Number(stats.speed));
    
    if (power < 1 || power > 100 || magic < 1 || magic > 100 || speed < 1 || speed > 100) {
      throw new Error('Stats out of valid range (1-100)');
    }
    
    // Validate balance requirements
    if (!validateStatBalance(power, magic, speed)) {
      throw new Error('Stats do not meet balance requirements (need one strong, one moderate, one weak)');
    }
    
    console.log(`Stats generation successful on attempt ${attempt}:`, { power, magic, speed });
    return { power, magic, speed };
    
  } catch (error) {
    console.log(`Stats generation failed on attempt ${attempt}:`, error.message);
    
    if (attempt < 3) {
      console.log(`Retrying stats generation, attempt ${attempt + 1}`);
      return generateStats(characterName, imageDescription, superpower, attempt + 1);
    }
    
    console.log('All retry attempts failed, using fallback stats');
    throw error;
  }
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { characterName, imageDescription, superpower } = await req.json();

    if (!openAIApiKey) {
      throw new Error('OpenAI API key not configured');
    }

    if (!characterName || !imageDescription || !superpower) {
      throw new Error('Missing required parameters: characterName, imageDescription, superpower');
    }

    console.log('Generating stats for character:', characterName);

    let stats;
    try {
      stats = await generateStats(characterName, imageDescription, superpower);
    } catch (error) {
      console.log('Using fallback stats due to generation failure:', error.message);
      stats = FALLBACK_STATS;
    }

    return new Response(JSON.stringify({ 
      power: stats.power,
      magic: stats.magic,
      speed: stats.speed,
      generated: stats !== FALLBACK_STATS
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error in generate-character-stats function:', error);
    return new Response(JSON.stringify({ 
      error: error.message,
      power: FALLBACK_STATS.power,
      magic: FALLBACK_STATS.magic,
      speed: FALLBACK_STATS.speed,
      generated: false
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});