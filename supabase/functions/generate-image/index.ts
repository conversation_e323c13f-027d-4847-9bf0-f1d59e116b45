import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
const supabaseUrl = Deno.env.get('SUPABASE_URL');
const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS'
};

// Model-agnostic prompt composer with kid-friendly semi-realistic defaults
const BASE_STYLE = "Kid-friendly, semi-realistic, slightly animated style. Single subject, centered composition. Clean, simple background. Vivid but balanced colors, soft light. No text, letters, words, numbers, signs, or symbols.";

function composeImagePrompt({ userPrompt, isEdit, originalContext }: { userPrompt: string; isEdit: boolean; originalContext?: string; }): string {
  if (isEdit) {
    const subject = originalContext ? originalContext : 'the existing character';
    return `Keep the same character identity and pose from ${subject}. Apply these changes: ${userPrompt}. ${BASE_STYLE}`;
  }
  return `A ${BASE_STYLE} illustration of ${userPrompt}.`;
}

// Detect if user is making an edit request (style change instructions)
function detectEditRequest(prompt: string): boolean {
  const lowered = prompt.toLowerCase();
  const editKeywords = [
    'make it', 'make the', 'make this',
    'more realistic', 'more detailed', 'more boyish',
    'less cute', 'less cartoon', 'less magical',
    'change it', 'transform', 'adjust',
    'but realistic', 'but more', 'but less'
  ];
  
  return editKeywords.some(keyword => lowered.includes(keyword));
}


serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  // Make these available in catch for error handling
  let promptHistoryId: string | null = null;
  let supabase: any = null;

  try {
    if (!openAIApiKey || !supabaseUrl || !supabaseKey) {
      throw new Error('Required environment variables not configured');
    }

const { 
  mode = 'create', 
  prompt, 
  baseImageUrl, 
  editInstructions, 
  originalPrompt,
  signature,
  baseImage, 
  styleTransfer,
  userId,
  sessionId,
  aspect = 'portrait', // 'portrait' | 'square' | 'landscape'
  transparentBackground = false
} = await req.json();

    // Support both old and new API
    const isEditMode = mode === 'edit' || (baseImageUrl && editInstructions);
    const finalPrompt = isEditMode ? editInstructions : prompt;
    const finalBaseImage = baseImageUrl || baseImage;

    if (!finalPrompt) {
      throw new Error('Prompt or edit instructions are required');
    }

    console.log('Function version: 2025-01-24-v4-context-aware-editing');
    console.log('OpenAI API Key present:', !!openAIApiKey);
    console.log('Mode:', mode, 'Is Edit Mode:', isEditMode);
    console.log('Final prompt:', finalPrompt);
    console.log('Original prompt context:', originalPrompt);
    console.log('Base image URL:', finalBaseImage ? 'Present' : 'None');

    // Initialize Supabase client
    supabase = createClient(supabaseUrl, supabaseKey);

    // Load image generation settings
    const { data: settings, error: settingsError } = await supabase
      .from('image_generation_settings')
      .select('dalle_settings')
      .maybeSingle();

    if (settingsError) {
      console.error('Error loading image settings:', settingsError);
    }

    // Get quality setting, fallback to 'standard' if not found
    const imageQuality = settings?.dalle_settings?.quality || 'standard';
    console.log('Using image quality:', imageQuality);

    // Create initial prompt history record
    try {
      const { data: promptHistory, error: promptError } = await supabase
        .from('prompt_history')
        .insert({
          user_prompt: finalPrompt,
          processed_prompt: '', // Will be updated after processing
          user_id: userId || null,
          session_id: sessionId || null,
          generation_status: 'processing',
          image_quality: imageQuality,
          metadata: {
            mode,
            has_base_image: !!finalBaseImage,
            has_edit_instructions: !!editInstructions,
            original_prompt: originalPrompt || null
          }
        })
        .select('id')
        .single();
      
      if (promptError) {
        console.error('Error creating prompt history record:', promptError);
      } else {
        promptHistoryId = promptHistory.id;
        console.log('Created prompt history record:', promptHistoryId);
      }
    } catch (historyError) {
      console.error('Failed to create prompt history:', historyError);
    }

// Compose prompt with kid-friendly, semi-realistic defaults
const processedPrompt = composeImagePrompt({ userPrompt: finalPrompt, isEdit: isEditMode, originalContext: originalPrompt });
console.log('Using processed prompt:', processedPrompt);

// Update prompt history with processed prompt
if (promptHistoryId) {
  try {
    await supabase
      .from('prompt_history')
      .update({ 
        processed_prompt: processedPrompt,
        generation_status: 'calling_image_api'
      })
      .eq('id', promptHistoryId);
  } catch (updateError) {
    console.error('Error updating prompt history with processed prompt:', updateError);
  }
}

// Generate image with OpenAI (primary: gpt-image-1, fallback: DALL·E 3)
let imageBlob: Blob | null = null;
let modelUsed: 'gpt-image-1' | 'dall-e-3' = 'gpt-image-1';
let revisedPrompt: string | null = null;

// Determine size from aspect
const size = aspect === 'landscape' ? '1536x1024' : (aspect === 'square' ? '1024x1024' : '1024x1536');
let imageSizeUsed = size; // track actual size used across model fallbacks
const background = transparentBackground ? 'transparent' : 'opaque';

try {
  if (isEditMode && finalBaseImage) {
    console.log('Using gpt-image-1 edit mode with identity preservation');
    const baseRes = await fetch(finalBaseImage);
    if (!baseRes.ok) throw new Error('Failed to fetch base image for edit');
    const baseBlob = await baseRes.blob();
    const form = new FormData();

    const editPrompt = `Keep the same character identity and pose from the provided image. Apply these changes: ${processedPrompt}. ${BASE_STYLE}`;

    form.append('model', 'gpt-image-1');
    form.append('prompt', editPrompt);
    form.append('size', size);
    form.append('background', background);
    form.append('n', '1');
    form.append('output_format', 'png');
    form.append('image', new File([baseBlob], 'base.png', { type: baseBlob.type || 'image/png' }));

    const resp = await fetch('https://api.openai.com/v1/images/edits', {
      method: 'POST',
      headers: { 'Authorization': `Bearer ${openAIApiKey}` },
      body: form,
    });
    if (!resp.ok) {
      const t = await resp.text();
      throw new Error(`gpt-image-1 edit error ${resp.status}: ${t}`);
    }
    const json = await resp.json();
    const b64 = json.data?.[0]?.b64_json;
    if (!b64) throw new Error('No image data from gpt-image-1 edit');
    const binary = atob(b64);
    const bytes = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) bytes[i] = binary.charCodeAt(i);
    imageBlob = new Blob([bytes], { type: 'image/png' });
  } else {
    console.log('Using gpt-image-1 generation');

    // Map quality
    const qualityMap: Record<string, string> = { standard: 'high', hd: 'high', high: 'high', medium: 'medium', low: 'low', auto: 'auto' };
    const mappedQuality = qualityMap[imageQuality] || 'high';

    const body = {
      model: 'gpt-image-1',
      prompt: processedPrompt,
      size,
      background,
      n: 1,
      output_format: 'png',
      quality: mappedQuality,
    };

    const resp = await fetch('https://api.openai.com/v1/images/generations', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });
    if (!resp.ok) {
      const t = await resp.text();
      throw new Error(`gpt-image-1 generation error ${resp.status}: ${t}`);
    }
    const json = await resp.json();
    const b64 = json.data?.[0]?.b64_json;
    if (!b64) throw new Error('No image data from gpt-image-1 generation');
    const binary = atob(b64);
    const bytes = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) bytes[i] = binary.charCodeAt(i);
    imageBlob = new Blob([bytes], { type: 'image/png' });
  }
} catch (e) {
  console.error('gpt-image-1 failed, falling back to DALL·E 3:', e);
  modelUsed = 'dall-e-3';

  // Map aspect to allowed DALL·E 3 sizes
  const dalleSize = aspect === 'landscape' ? '1792x1024' : (aspect === 'square' ? '1024x1024' : '1024x1792');
  imageSizeUsed = dalleSize;

  let requestBody: any;
  let apiEndpoint = 'https://api.openai.com/v1/images/generations';

  if (isEditMode && finalBaseImage) {
    const subjectContext = originalPrompt || 'the character in the image';
    const contextPreservationPrompt = `Transform this existing character (${subjectContext}). Apply these changes: ${processedPrompt}. ${BASE_STYLE}`;
    requestBody = {
      model: 'dall-e-3',
      prompt: contextPreservationPrompt,
      size: dalleSize,
      n: 1,
      quality: imageQuality,
      style: 'vivid'
    };
  } else {
    requestBody = {
      model: 'dall-e-3',
      prompt: processedPrompt,
      size: dalleSize,
      n: 1,
      quality: imageQuality,
      style: 'vivid'
    };
  }

  const headers: any = {
    'Authorization': `Bearer ${openAIApiKey}`,
    'Content-Type': 'application/json',
  };

  const response = await fetch(apiEndpoint, {
    method: 'POST',
    headers,
    body: JSON.stringify(requestBody),
  });

  if (!response.ok) {
    const errorData = await response.text();
    console.error('OpenAI DALL·E API error:', response.status, errorData);
    throw new Error(`OpenAI API error: ${response.status}`);
  }

  const data = await response.json();
  console.log('Image generated successfully via DALL·E');

  revisedPrompt = data.data?.[0]?.revised_prompt || null;

  const imageData = data.data[0];
  if (imageData.url) {
    const imageResponse = await fetch(imageData.url);
    if (!imageResponse.ok) {
      throw new Error('Failed to download generated image');
    }
    imageBlob = await imageResponse.blob();
  } else if (imageData.b64_json) {
    const base64Data = imageData.b64_json;
    const binaryData = atob(base64Data);
    const uint8Array = new Uint8Array(binaryData.length);
    for (let i = 0; i < binaryData.length; i++) {
      uint8Array[i] = binaryData.charCodeAt(i);
    }
    imageBlob = new Blob([uint8Array], { type: 'image/png' });
  } else {
    console.error('No image data received from DALL·E:', imageData);
    throw new Error('No image data received from OpenAI');
  }
}

if (!imageBlob) {
  throw new Error('Image generation failed');
}

// Generate unique filename
const filename = `character-${Date.now()}-${Math.random().toString(36).substring(7)}.png`;

// Upload to Supabase Storage
const { data: uploadData, error: uploadError } = await supabase.storage
  .from('character-images')
  .upload(filename, imageBlob, {
    contentType: 'image/png',
    cacheControl: '3600'
  });

if (uploadError) {
  console.error('Supabase upload error:', uploadError);
  throw new Error(`Failed to upload image: ${uploadError.message}`);
}

// Get public URL
const { data: { publicUrl } } = supabase.storage
  .from('character-images')
  .getPublicUrl(filename);

console.log('Image uploaded successfully to:', publicUrl);

// Update prompt history with success (card_id will be updated later by the frontend when card is created)
if (promptHistoryId) {
  try {
    await supabase
      .from('prompt_history')
      .update({ 
        generation_status: 'success',
        image_url: publicUrl,
        dalle_revised_prompt: revisedPrompt,
        metadata: {
          mode,
          has_base_image: !!finalBaseImage,
          has_edit_instructions: !!editInstructions,
          original_prompt: originalPrompt || null,
          model: modelUsed,
          image_size: imageSizeUsed,
          image_background: background,
          prompt_history_id: promptHistoryId // Include this for frontend to link card
        }
      })
      .eq('id', promptHistoryId);
  } catch (updateError) {
    console.error('Error updating prompt history with success:', updateError);
  }
}

return new Response(JSON.stringify({ 
  imageUrl: publicUrl,
  imageDescription: revisedPrompt || finalPrompt,
  mode: isEditMode ? 'edit' : 'create',
  promptHistoryId: promptHistoryId // Return this for linking to cards
}), {
  headers: { ...corsHeaders, 'Content-Type': 'application/json' },
});
  } catch (error) {
    console.error('Error in generate-image function:', error);
    
    // Update prompt history with error if we have a record
    if (promptHistoryId && supabase) {
      try {
        await supabase
          .from('prompt_history')
          .update({ 
            generation_status: 'error',
            error_message: error.message || 'Unknown error occurred'
          })
          .eq('id', promptHistoryId);
      } catch (updateError) {
        console.error('Error updating prompt history with error:', updateError);
      }
    }
    
    return new Response(JSON.stringify({ 
      error: error.message || 'An unexpected error occurred' 
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});